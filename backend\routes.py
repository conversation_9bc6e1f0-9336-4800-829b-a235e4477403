from fastapi import FastAPI, APIRouter
from app.auth import router as auth_router
from app.text.translate import router as translate_router
from app.text.copywriting import router as copywriting_router
from app.manage.gameManage import router as gameManage_router
from app.systemManage.user import router as system_user_router
from app.systemManage.userCredit import router as user_credit_router
from app.systemManage.role import router as role_router
from app.systemManage.menu import router as menu_router
from app.systemManage.creditLog import router as credit_log_router
from app.systemManage.setting import router as setting_router
from app.manage.toolset import router as toolset_router
from app.systemManage.modelPrice import router as modelPrice_router
from app.common.upload import router as upload_router
from app.portrait.cutout import router as cutout_router
from app.portrait.subtitle import router as subtitle_router
from app.portrait.voice import router as voice_router
from app.portrait.enlarge import router as enlarge_router
from app.portrait.cosy import router as cosy_router
from app.portrait.aidraw import router as aidraw_router
from app.portrait.facecopy import router as facecopy_router
from app.portrait.mimicbrush import router as mimicbrush_router
from app.user.user import router as user_router
from app.portrait.midjourney import router as midjourney_router
from app.log import router as log_router
from app.portrait.volcano import router as volcano_router
from app.chat.assistant import router as assistant_router
from app.text.imgocr import router as imgocr_router
from app.audio.reduction import router as reduction_router
from app.ainews import router as ainews_router
from app.portrait.sd import router as sd_router
from app.audio.clearvoice import router as clearvoice_router
from app.audio.inspiremusic import router as inspiremusic_router
from app.video.framepack import router as framepack_router
from app.api_request_time import router as api_time_router
from app.systemManage.teams import router as teams_router
from app.systemManage.config import router as config_router
from app.cos_upload import router as cos_upload_router
from app.video.framepack import router as framepack_router
from app.video.volcengine import router as volcengine_router
from app.systemManage.model import router as model_router
from app.task.task_webhook import router as task_webhook_router
from app.portrait.gptimage import router as gptimage_router
from app.portrait.fluximage import router as fluximage_router
from app.share.api import router as share_router
from app.asset_manage.user_assets import router as user_assets_router
from app.asset_manage.asset_report import router as asset_report_router
from app.audio.tones import router as tones_router
from app.audio.synthesis import router as synthesis_router


def add_routes(app: FastAPI) -> None:
  app.include_router(
    auth_router,
    prefix="/auth",
    tags=["auth"],
  )

  app.include_router(
    translate_router,
    prefix="/text",
    tags=["text"],
  )

  app.include_router(
    copywriting_router,
    prefix="/text",
    tags=["text"],
  )

  app.include_router(
    subtitle_router,
    prefix="/media",
    tags=["media"],
  )

  app.include_router(
    gameManage_router,
    prefix="/gameManage",
    tags=["manage"],
  )

  app.include_router(
    modelPrice_router,
    prefix="/modelPrice",
    tags=["manage"],
  )

  app.include_router(
    credit_log_router,
    prefix="/creditLog",
    tags=["manage"],
  )

  app.include_router(
    user_credit_router,
    prefix="/usercredit",
    tags=["manage"],
  )

  app.include_router(
    toolset_router,
    prefix="/toolset",
    tags=["toolset"],
  )

  app.include_router(
    upload_router,
    prefix="/common",
    tags=["common"],
  )

  app.include_router(
    cutout_router,
    prefix="/cutout",
    tags=["cutout"],
  )

  app.include_router(
    voice_router,
    prefix="/media",
    tags=["media"],
  )

  app.include_router(
    enlarge_router,
    prefix="/enlarge",
    tags=["enlarge"],
  )

  app.include_router(
    cosy_router,
    prefix="/cosy",
    tags=["cosy"],
  )

  app.include_router(
    aidraw_router,
    prefix="/aidraw",
    tags=["aidraw"],
  )

  app.include_router(
    facecopy_router,
    prefix="/facecopy",
    tags=["facecopy"],
  )

  app.include_router(
    mimicbrush_router,
    prefix="/mimicbrush",
    tags=["mimicbrush"],
  )

  app.include_router(
    user_router,
    prefix="/user",
    tags=["user_router"],
  )

  # app.include_router(
  #   setting_router,
  #   prefix="/managersetting",
  #   tags=["system"],
  # )

  app.include_router(
    log_router,
    prefix="/log",
    tags=["log"],
  )

  app.include_router(
    midjourney_router,
    prefix="/midjourney",
    tags=["midjourney"],
  )

  app.include_router(
    volcano_router,
    prefix="/volcano",
    tags=["volcano"],
  )

  app.include_router(
    assistant_router,
    prefix="/assistant",
    tags=["assistant"],
  )

  app.include_router(
    imgocr_router,
    prefix="/imgocr",
    tags=["imgocr"],
  )

  app.include_router(
    reduction_router,
    prefix="/reduction",
    tags=["reduction"],
  )

  app.include_router(
    ainews_router,
    prefix="/ainews",
    tags=["ainews"],
  )

  app.include_router(
    sd_router,
    prefix="/sd",
    tags=["sd"],
  )

  app.include_router(
    clearvoice_router,
    prefix="/clearvoice",
    tags=["clearvoice"],
  )

  app.include_router(
    inspiremusic_router,
    prefix="/inspiremusic",
    tags=["inspiremusic"],
  )

  app.include_router(
    framepack_router,
    prefix="/framepack",
    tags=["framepack"],
  )

  app.include_router(
    api_time_router,
    tags=["system"],
  )

  app.include_router(
    config_router,
    prefix="/config",
    tags=["config"],
  )

  app.include_router(
    cos_upload_router,
    prefix="/cos",
    tags=["cos"],
  )

  app.include_router(
    framepack_router,
    prefix="/framepack",
    tags=["framepack"],
  )

  app.include_router(
    volcengine_router,
    prefix="/volcengine",
    tags=["volcengine"],
  )

  app.include_router(
    model_router,
    prefix="/models",
    tags=["model"],
  )

  app.include_router(
    task_webhook_router,
    prefix="/task",
    tags=["task"],
  )

  app.include_router(
    gptimage_router,
    prefix="/gptimage",
    tags=["gptimage"],
  )

  app.include_router(
    fluximage_router,
    prefix="/flux",
    tags=["flux"],
  )

  app.include_router(
    share_router,
    prefix="/share",
    tags=["share"],
  )

  app.include_router(
    user_assets_router,
    prefix="/asset",
    tags=["asset"],
  )

  app.include_router(
    asset_report_router,
    prefix="/asset",
    tags=["asset"],
  )

  app.include_router(
    tones_router,
    prefix="/tones",
    tags=["audio", "tones"],
  )

  app.include_router(
    synthesis_router,
    prefix="/synthesis",
    tags=["audio", "synthesis"],
  )

  # 系统管理
  system_api_router = APIRouter(prefix="/system", tags=["system"])
  sub_routers = [system_user_router, role_router, menu_router, setting_router, teams_router]
  for router in sub_routers:
    system_api_router.include_router(router)
  app.include_router(system_api_router)
