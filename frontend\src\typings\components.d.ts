/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    ATour: typeof import('ant-design-vue/es')['Tour']
    Audio: typeof import('./../components/audio/index.vue')['default']
    AudioPlayer: typeof import('./../components/audio/AudioPlayer.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    CardSelect: typeof import('./../components/custom/card-select.vue')['default']
    Collect: typeof import('./../components/collect/index.vue')['default']
    CollectButton: typeof import('./../components/collect/collect-button.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconMdiCloudUpload: typeof import('~icons/mdi/cloud-upload')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    ModelPrice: typeof import('./../components/credit/model-price.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAutoComplete: typeof import('naive-ui')['NAutoComplete']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBackTop: typeof import('naive-ui')['NBackTop']
    NBadge: typeof import('naive-ui')['NBadge']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCascader: typeof import('naive-ui')['NCascader']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialog: typeof import('naive-ui')['NDialog']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEl: typeof import('naive-ui')['NEl']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFelx: typeof import('naive-ui')['NFelx']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGrid: typeof import('naive-ui')['NGrid']
    NGridItem: typeof import('naive-ui')['NGridItem']
    NH2: typeof import('naive-ui')['NH2']
    NH4: typeof import('naive-ui')['NH4']
    NH5: typeof import('naive-ui')['NH5']
    NIcon: typeof import('naive-ui')['NIcon']
    NImage: typeof import('naive-ui')['NImage']
    NImageGroup: typeof import('naive-ui')['NImageGroup']
    NInfiniteScroll: typeof import('naive-ui')['NInfiniteScroll']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputGroupLabel: typeof import('naive-ui')['NInputGroupLabel']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NLayout: typeof import('naive-ui')['NLayout']
    NLayoutContent: typeof import('naive-ui')['NLayoutContent']
    NLayoutSider: typeof import('naive-ui')['NLayoutSider']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMarquee: typeof import('naive-ui')['NMarquee']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NNumberAnimation: typeof import('naive-ui')['NNumberAnimation']
    NOl: typeof import('naive-ui')['NOl']
    NP: typeof import('naive-ui')['NP']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NPopselect: typeof import('naive-ui')['NPopselect']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NRate: typeof import('naive-ui')['NRate']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTransfer: typeof import('naive-ui')['NTransfer']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    PriceIndicator: typeof import('./../components/credit/price-indicator.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
